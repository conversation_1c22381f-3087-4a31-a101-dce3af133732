package main

import (
	"fmt"
	"gitee.com/rcztcs/zjl/config"
	"os"
	"os/signal"
	"syscall"

	"gitee.com/rcztcs/zjl/internal/pkg/logger"
	"gitee.com/rcztcs/zjl/internal/zjl/controller"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"github.com/spf13/cobra"
	_ "github.com/spf13/cobra"
	_ "go.mongodb.org/mongo-driver/bson"
	_ "go.uber.org/zap"
	_ "gorm.io/driver/mysql"
	_ "gorm.io/gorm"
)

func main() {
	var port *int32
	var dbURI *string

	sigQuit := make(chan os.Signal, 1)
	signal.Notify(sigQuit, syscall.SIGKILL, syscall.SIGINT, syscall.SIGQUIT, syscall.SIGTERM)

	rootCmd := &cobra.Command{
		Use:     "zjl [flags]",
		Example: `zjl -h`,
		RunE: func(cmd *cobra.Command, args []string) error {
			// 日志系统初始化
			if err := logger.Init(); err != nil {
				return err
			}

			// 配置初始化
			if err := config.Init(); err != nil {
				return err
			}
			conf := config.GetConfig()

			// 如果没有通过命令行指定数据库连接，则使用配置文件中的值
			if *dbURI == "" {
				if conf != nil && conf.DbURL != "" {
					*dbURI = conf.DbURL
				} else {
					return fmt.Errorf("数据库连接信息未配置")
				}
			}

			// 数据库初始化
			if err := model.Init(*dbURI); err != nil {
				return err
			}

			// web服务初始化
			if err := controller.Init(fmt.Sprintf(":%d", *port), conf.JwtSecretKey); err != nil {
				return err
			}

			select {
			case <-sigQuit:
				//TODO 各个模块退出
			}
			return nil
		},
	}

	port = rootCmd.Flags().Int32P("port", "p", 8080, "web监听端口")
	dbURI = rootCmd.Flags().StringP("db", "d", "", "数据库连接信息")
	if err := rootCmd.Execute(); err != nil {
		fmt.Printf("启动失败: %v\n", err)
	}
}
