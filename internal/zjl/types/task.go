package types

import (
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"time"
)

// CreateTaskRequest 创建任务请求
type CreateTaskRequest struct {
	ReportID string `json:"report_id" form:"reportId" binding:"required"` // 报告ID
	Token    string `json:"token" form:"token" binding:"required"`        // 令牌
	Host     string `json:"host" form:"host" binding:"required"`          // 主机
}

// CreateTaskResponse 创建任务响应
type CreateTaskResponse struct {
	UserID   uint   `json:"user_id"`
	Password string `json:"password"`
	Code     string `json:"code"`
	Msg      string `json:"msg"`
}

// GetTaskRequest 获取任务详情请求
type GetTaskRequest struct {
	ID uint `uri:"id" binding:"required"`
}

// GetTaskResponse 获取任务详情响应
type GetTaskResponse struct {
	Task *model.Task `json:"task"`
}

// ListTasksRequest 任务列表查询请求
type ListTasksRequest struct {
	Page       int               `json:"page" form:"page"`               // 页码，默认1
	PageSize   int               `json:"page_size" form:"page_size"`     // 每页数量，默认10
	UserID     *uint             `json:"user_id" form:"user_id"`         // 用户ID筛选，可选
	CreditCode string            `json:"credit_code" form:"credit_code"` // 信用代码筛选，可选
	Status     *model.TaskStatus `json:"status" form:"status"`           // 状态筛选，可选
	StartTime  *time.Time        `json:"start_time" form:"start_time"`   // 开始时间筛选，可选
	EndTime    *time.Time        `json:"end_time" form:"end_time"`       // 结束时间筛选，可选
}

// SetDefaultPagination 设置默认分页参数
func (r *ListTasksRequest) SetDefaultPagination() {
	if r.Page <= 0 {
		r.Page = 1
	}
	if r.PageSize <= 0 {
		r.PageSize = 10
	}
}

// ListTasksResponse 任务列表查询响应
type ListTasksResponse struct {
	Tasks []*model.Task `json:"tasks"`
	Total int64         `json:"total"`
}

// UpdateTaskStatusRequest 更新任务状态请求
type UpdateTaskStatusRequest struct {
	ID     uint             `uri:"id" binding:"required"`
	Status model.TaskStatus `json:"status" binding:"required"`
}

// UpdateTaskStatusResponse 更新任务状态响应
type UpdateTaskStatusResponse struct {
	Task *model.Task `json:"task"`
}
