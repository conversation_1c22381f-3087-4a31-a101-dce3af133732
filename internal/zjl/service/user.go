package service

import (
	"crypto/md5"
	"errors"
	"fmt"
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/dao"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
)

// UserService 用户服务层接口
type UserService interface {
	Login(username, password string) (*model.User, error)
	GetUserInfo(id uint) (*model.User, error)
	ResetPassword(id uint, newPassword string) error
	UpdateUserStatus(id uint, status model.UserStatus) error
	CreateUser(nickname, username, password, mobile string) error
	UpdateUser(user *model.User) error
	ListUsers(page, pageSize int, nickname string, status *model.UserStatus) ([]*model.User, int64, error)
}

// userService 用户服务层实现
type userService struct {
	userDao dao.UserDao
}

// NewUserService 创建用户服务层实例
func NewUserService(userDao dao.UserDao) UserService {
	return &userService{
		userDao: userDao,
	}
}

// Login 用户登录
func (s *userService) Login(username, password string) (*model.User, error) {
	if username == "" || password == "" {
		return nil, utils.ErrPasswordWrong
	}
	// 根据用户名获取用户
	user, err := s.userDao.GetByUsername(username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, utils.ErrPasswordWrong
		}
		return nil, err
	}

	// 检查用户状态
	if user.Status == model.UserStatusDisabled {
		return nil, utils.ErrForbidden
	}

	// 验证密码
	if !s.verifyPassword(password, user.Password) {
		return nil, utils.ErrPasswordWrong
	}

	return user, nil
}

// GetUserInfo 获取用户信息
func (s *userService) GetUserInfo(id uint) (*model.User, error) {
	if id == 0 {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "用户ID不能为空")
	}

	user, err := s.userDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, utils.NewAppError(utils.ErrUserNotFoundCode, "用户不存在")
		}
		return nil, err
	}

	return user, nil
}

// ResetPassword 重置密码
func (s *userService) ResetPassword(id uint, newPassword string) error {
	if id == 0 {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "用户ID不能为空")
	}
	if newPassword == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "新密码不能为空")
	}

	// 检查用户是否存在
	_, err := s.userDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return utils.NewAppError(utils.ErrUserNotFoundCode, "用户不存在")
		}
		return err
	}

	// 加密新密码
	hashedPassword := s.hashPassword(newPassword)

	// 更新密码
	return s.userDao.UpdatePassword(id, hashedPassword)
}

// UpdateUserStatus 更新用户状态
func (s *userService) UpdateUserStatus(id uint, status model.UserStatus) error {
	if id == 0 {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "用户ID不能为空")
	}

	// 验证状态值
	if status != model.UserStatusEnabled && status != model.UserStatusDisabled {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "无效的用户状态")
	}

	// 检查用户是否存在
	_, err := s.userDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return utils.NewAppError(utils.ErrUserNotFoundCode, "用户不存在")
		}
		return err
	}

	return s.userDao.UpdateStatus(id, status)
}

// CreateUser 创建用户
func (s *userService) CreateUser(nickname, username, password, mobile string) error {
	if nickname == "" || username == "" || password == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "昵称、用户名和密码不能为空")
	}

	// 检查用户名是否已存在
	exists, err := s.userDao.IsUsernameExist(username)
	if err != nil {
		return err
	}
	if exists {
		return utils.ErrUsernameExist
	}

	// 检查手机号是否已存在（如果提供了手机号）
	if mobile != "" {
		exists, err := s.userDao.IsMobileExist(mobile)
		if err != nil {
			return err
		}
		if exists {
			return utils.ErrMobileExist
		}
	}

	// 创建用户
	user := &model.User{
		Nickname: nickname,
		Username: username,
		Password: s.hashPassword(password),
		Mobile:   mobile,
		Status:   model.UserStatusEnabled,
	}

	return s.userDao.Create(user)
}

// UpdateUser 更新用户信息
func (s *userService) UpdateUser(user *model.User) error {
	if user.ID == 0 {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "用户ID不能为空")
	}

	// 检查用户是否存在
	_, err := s.userDao.GetByID(user.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return utils.NewAppError(utils.ErrUserNotFoundCode, "用户不存在")
		}
		return err
	}

	return s.userDao.Update(user)
}

// ListUsers 获取用户列表
func (s *userService) ListUsers(page, pageSize int, nickname string, status *model.UserStatus) ([]*model.User, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 获取总数
	total, err := s.userDao.CountWithStatus(nickname, status)
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	users, err := s.userDao.ListWithStatus(page, pageSize, nickname, status)
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// hashPassword 密码加密
func (s *userService) hashPassword(password string) string {
	hash := md5.Sum([]byte(password))
	return fmt.Sprintf("%x", hash)
}

// verifyPassword 验证密码
func (s *userService) verifyPassword(password, hashedPassword string) bool {
	return s.hashPassword(password) == hashedPassword
}
