package service

import (
	"crypto/rand"
	"encoding/hex"
	"errors"
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/dao"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
	"time"
)

// TaskService 任务服务层接口
type TaskService interface {
	// CreateTask 创建任务
	CreateTask(reportID, token, host string) (*CreateTaskResult, error)

	// GetTaskByID 根据ID获取任务详情
	GetTaskByID(id uint) (*model.Task, error)

	// ListTasksByUserID 根据用户ID查询任务列表
	ListTasksByUserID(userID uint, page, pageSize int) ([]*model.Task, int64, error)

	// ListTasksByCreditCode 根据信用代码查询任务列表
	ListTasksByCreditCode(creditCode string, page, pageSize int) ([]*model.Task, int64, error)

	// ListTasksByTimeRange 根据时间区间查询任务列表
	ListTasksByTimeRange(startTime, endTime time.Time, page, pageSize int) ([]*model.Task, int64, error)

	// ListTasks 分页查询任务列表（支持多条件筛选）
	ListTasks(page, pageSize int, userID *uint, creditCode string, status *model.TaskStatus, startTime, endTime *time.Time) ([]*model.Task, int64, error)

	// UpdateTaskStatus 更新任务状态
	UpdateTaskStatus(id uint, status model.TaskStatus) error
}

// CreateTaskResult 创建任务结果
type CreateTaskResult struct {
	UserID   uint   `json:"user_id"`
	Password string `json:"password"`
	Code     string `json:"code"`
	Msg      string `json:"msg"`
}

// taskService 任务服务层实现
type taskService struct {
	taskDao dao.TaskDao
}

// NewTaskService 创建任务服务层实例
func NewTaskService(taskDao dao.TaskDao) TaskService {
	return &taskService{
		taskDao: taskDao,
	}
}

// CreateTask 创建任务
func (s *taskService) CreateTask(reportID, token, host string) (*CreateTaskResult, error) {
	// TODO 还需要信用代码等参数

	// 生成随机用户ID（这里简化处理，实际应该根据业务逻辑生成）
	userID := uint(time.Now().Unix() % 100000)

	// 生成随机密码
	password, err := s.generateRandomPassword(8)
	if err != nil {
		return nil, utils.NewAppError(utils.ErrInternalCode, "生成密码失败")
	}

	// 生成随机信用代码
	creditCode, err := s.generateRandomCode(12)
	if err != nil {
		return nil, utils.NewAppError(utils.ErrInternalCode, "生成信用代码失败")
	}

	// 创建任务
	task := &model.Task{
		UserID:     userID,
		CreditCode: creditCode,
		Status:     model.TaskStatusPending,
		ReportID:   reportID,
		Token:      token,
		Host:       host,
	}

	err = s.taskDao.Create(task)
	if err != nil {
		return nil, utils.NewAppError(utils.ErrInternalCode, "创建任务失败")
	}

	return &CreateTaskResult{
		UserID:   userID,
		Password: password,
		Code:     "200",
		Msg:      "任务创建成功",
	}, nil
}

// GetTaskByID 根据ID获取任务详情
func (s *taskService) GetTaskByID(id uint) (*model.Task, error) {
	task, err := s.taskDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, utils.NewAppError(utils.ErrNotFoundCode, "任务不存在")
		}
		return nil, err
	}
	return task, nil
}

// ListTasksByUserID 根据用户ID查询任务列表
func (s *taskService) ListTasksByUserID(userID uint, page, pageSize int) ([]*model.Task, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	return s.taskDao.ListByUserID(userID, page, pageSize)
}

// ListTasksByCreditCode 根据信用代码查询任务列表
func (s *taskService) ListTasksByCreditCode(creditCode string, page, pageSize int) ([]*model.Task, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	return s.taskDao.ListByCreditCode(creditCode, page, pageSize)
}

// ListTasksByTimeRange 根据时间区间查询任务列表
func (s *taskService) ListTasksByTimeRange(startTime, endTime time.Time, page, pageSize int) ([]*model.Task, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	return s.taskDao.ListByTimeRange(startTime, endTime, page, pageSize)
}

// ListTasks 分页查询任务列表（支持多条件筛选）
func (s *taskService) ListTasks(page, pageSize int, userID *uint, creditCode string, status *model.TaskStatus, startTime, endTime *time.Time) ([]*model.Task, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	return s.taskDao.List(page, pageSize, userID, creditCode, status, startTime, endTime)
}

// UpdateTaskStatus 更新任务状态
func (s *taskService) UpdateTaskStatus(id uint, status model.TaskStatus) error {
	task, err := s.taskDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return utils.NewAppError(utils.ErrNotFoundCode, "任务不存在")
		}
		return err
	}

	task.Status = status
	return s.taskDao.Update(task)
}

// generateRandomPassword 生成随机密码
func (s *taskService) generateRandomPassword(length int) (string, error) {
	bytes := make([]byte, length)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes)[:length], nil
}

// generateRandomCode 生成随机代码
func (s *taskService) generateRandomCode(length int) (string, error) {
	bytes := make([]byte, length/2)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes)[:length], nil
}
