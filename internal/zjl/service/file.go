package service

import (
	"crypto/md5"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gitee.com/rcztcs/zjl/internal/pkg/logger"
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/dao"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// FileService 文件服务层接口
type FileService interface {
	// UploadFile 上传文件
	UploadFile(file *multipart.FileHeader, uploadUserID uint, dir string) (*model.File, error)

	// DownloadFile 下载文件
	DownloadFile(storageName string) (*model.File, string, error)

	// GetFileInfo 获取文件信息
	GetFileInfo(storageName string) (*model.File, error)

	// ListFiles 分页查询文件列表
	ListFiles(page, pageSize int, uploadUserID *uint, contentType string) ([]*model.File, int64, error)
}

// fileService 文件服务层实现
type fileService struct {
	fileDao     dao.FileDao
	uploadPath  string
	maxFileSize int64
	allowedExts []string
}

// NewFileService 创建文件服务层实例
func NewFileService(fileDao dao.FileDao) FileService {
	// 创建上传目录
	uploadPath := "./uploads"
	if err := os.MkdirAll(uploadPath, 0755); err != nil {
		logger.Logger().Error("创建上传目录失败", zap.Error(err))
	}

	return &fileService{
		fileDao:     fileDao,
		uploadPath:  uploadPath,
		maxFileSize: 10 * 1024 * 1024, // 10MB
		allowedExts: []string{".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".zip", ".rar"},
	}
}

// UploadFile 上传文件
func (s *fileService) UploadFile(file *multipart.FileHeader, uploadUserID uint, dir string) (*model.File, error) {
	if file == nil {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "文件不能为空")
	}

	// 检查文件大小
	if file.Size > s.maxFileSize {
		return nil, utils.ErrFileTooLarge
	}

	// 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(file.Filename))
	if !s.isAllowedExtension(ext) {
		return nil, utils.ErrFileTypeNotAllowed
	}

	// 打开上传的文件
	src, err := file.Open()
	if err != nil {
		logger.Logger().Error("打开上传文件失败", zap.Error(err))
		return nil, utils.ErrFileUploadFailed
	}
	defer src.Close()

	// 计算文件哈希
	hash, err := s.calculateFileHash(src)
	if err != nil {
		logger.Logger().Error("计算文件哈希失败", zap.Error(err))
		return nil, utils.ErrFileUploadFailed
	}

	// 检查文件是否已存在（去重）
	existingFile, err := s.fileDao.GetByHash(hash)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger().Error("查询文件哈希失败", zap.Error(err))
		return nil, utils.ErrInternalServer
	}

	if existingFile != nil {
		// 文件已存在，直接返回现有文件信息
		return existingFile, nil
	}

	// 生成存储文件名
	storageName := s.generateStorageName(file.Filename)

	// 构建文件存储路径
	targetDir := filepath.Join(s.uploadPath, dir)
	filePath := filepath.Join(targetDir, storageName)

	// 确保目标目录存在
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		logger.Logger().Error("创建目标目录失败", zap.Error(err), zap.String("dir", targetDir))
		return nil, utils.ErrFileUploadFailed
	}

	// 重置文件指针到开头
	src.Seek(0, io.SeekStart)

	// 创建目标文件
	dst, err := os.Create(filePath)
	if err != nil {
		logger.Logger().Error("创建目标文件失败", zap.Error(err))
		return nil, utils.ErrFileUploadFailed
	}
	defer dst.Close()

	// 复制文件内容
	_, err = io.Copy(dst, src)
	if err != nil {
		logger.Logger().Error("复制文件内容失败", zap.Error(err))
		// 删除已创建的文件
		os.Remove(filePath)
		return nil, utils.ErrFileUploadFailed
	}

	// 创建文件记录
	fileRecord := &model.File{
		FileName:     file.Filename,
		StorageName:  storageName,
		FilePath:     filePath,
		FileSize:     file.Size,
		ContentType:  file.Header.Get("Content-Type"),
		FileHash:     hash,
		UploadUserID: uploadUserID,
		Status:       model.FileStatusActive,
	}

	err = s.fileDao.Create(fileRecord)
	if err != nil {
		logger.Logger().Error("创建文件记录失败", zap.Error(err))
		// 删除已上传的文件
		os.Remove(filePath)
		return nil, utils.ErrFileUploadFailed
	}

	logger.Logger().Info("文件上传成功",
		zap.String("filename", file.Filename),
		zap.String("storage_name", storageName),
		zap.Int64("size", file.Size),
		zap.Uint("user_id", uploadUserID))

	return fileRecord, nil
}

// DownloadFile 下载文件
func (s *fileService) DownloadFile(storageName string) (*model.File, string, error) {
	// 获取文件信息
	file, err := s.fileDao.GetByStorageName(storageName)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, "", utils.ErrFileNotFound
		}
		logger.Logger().Error("获取文件信息失败", zap.Error(err))
		return nil, "", utils.ErrInternalServer
	}

	// 检查文件是否存在
	if _, err := os.Stat(file.FilePath); os.IsNotExist(err) {
		logger.Logger().Error("文件不存在", zap.String("path", file.FilePath))
		return nil, "", utils.ErrFileNotFound
	}

	// 更新下载信息
	err = s.fileDao.UpdateDownloadInfo(file.ID)
	if err != nil {
		logger.Logger().Error("更新下载信息失败", zap.Error(err))
		// 不影响下载，继续执行
	}

	logger.Logger().Info("文件下载",
		zap.String("storage_name", storageName),
		zap.String("filename", file.FileName))

	return file, file.FilePath, nil
}

// GetFileInfo 获取文件信息
func (s *fileService) GetFileInfo(storageName string) (*model.File, error) {
	file, err := s.fileDao.GetByStorageName(storageName)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, utils.ErrFileNotFound
		}
		return nil, err
	}
	return file, nil
}

// ListFiles 分页查询文件列表
func (s *fileService) ListFiles(page, pageSize int, uploadUserID *uint, contentType string) ([]*model.File, int64, error) {
	files, err := s.fileDao.List(page, pageSize, uploadUserID, contentType)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.fileDao.Count(uploadUserID, contentType)
	if err != nil {
		return nil, 0, err
	}

	return files, total, nil
}

// isAllowedExtension 检查文件扩展名是否允许
func (s *fileService) isAllowedExtension(ext string) bool {
	for _, allowedExt := range s.allowedExts {
		if ext == allowedExt {
			return true
		}
	}
	return false
}

// calculateFileHash 计算文件哈希值
func (s *fileService) calculateFileHash(file multipart.File) (string, error) {
	hash := md5.New()
	_, err := io.Copy(hash, file)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// generateStorageName 生成存储文件名
func (s *fileService) generateStorageName(originalName string) string {
	ext := filepath.Ext(originalName)
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("%d%s", timestamp, ext)
}
