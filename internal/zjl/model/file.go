package model

import (
	"gorm.io/gorm"
	"time"
)

// FileStatus 文件状态
type FileStatus int

const (
	FileStatusActive  FileStatus = 1 // 正常
	FileStatusDeleted FileStatus = 2 // 已删除
)

// File 文件模型
type File struct {
	gorm.Model
	FileName       string     `gorm:"column:file_name;type:varchar(255);not null" json:"file_name"`                 // 原始文件名
	StorageName    string     `gorm:"column:storage_name;type:varchar(255);not null;unique" json:"storage_name"`    // 存储文件名（唯一）
	FilePath       string     `gorm:"column:file_path;type:varchar(500);not null" json:"file_path"`                 // 文件存储路径
	FileSize       int64      `gorm:"column:file_size;type:bigint;not null" json:"file_size"`                       // 文件大小（字节）
	ContentType    string     `gorm:"column:content_type;type:varchar(100);not null" json:"content_type"`           // 文件MIME类型
	FileHash       string     `gorm:"column:file_hash;type:varchar(64);not null;index" json:"file_hash"`            // 文件哈希值（用于去重）
	UploadUserID   uint       `gorm:"column:upload_user_id;type:int unsigned;not null;index" json:"upload_user_id"` // 上传用户ID
	Status         FileStatus `gorm:"column:status;type:tinyint;not null;default:1;index" json:"status"`            // 文件状态
	DownloadCount  int64      `gorm:"column:download_count;type:bigint;not null;default:0" json:"download_count"`   // 下载次数
	LastDownloadAt *time.Time `gorm:"column:last_download_at;type:datetime" json:"last_download_at"`                // 最后下载时间
}

// TableName 指定表名
func (File) TableName() string {
	return "files"
}
