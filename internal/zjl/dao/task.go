package dao

import (
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
	"time"
)

// TaskDao 任务数据访问层接口
type TaskDao interface {
	// Create 创建任务
	Create(task *model.Task) error

	// GetByID 根据ID获取任务
	GetByID(id uint) (*model.Task, error)

	// Update 更新任务信息
	Update(task *model.Task) error

	// ListByUserID 根据用户ID查询任务列表
	ListByUserID(userID uint, page, pageSize int) ([]*model.Task, int64, error)

	// ListByCreditCode 根据信用代码查询任务列表
	ListByCreditCode(creditCode string, page, pageSize int) ([]*model.Task, int64, error)

	// ListByTimeRange 根据时间区间查询任务列表
	ListByTimeRange(startTime, endTime time.Time, page, pageSize int) ([]*model.Task, int64, error)

	// List 分页查询任务列表（支持多条件筛选）
	List(page, pageSize int, userID *uint, creditCode string, status *model.TaskStatus, startTime, endTime *time.Time) ([]*model.Task, int64, error)
}

// taskDao 任务数据访问层实现
type taskDao struct {
	db *gorm.DB
}

// NewTaskDao 创建任务数据访问层实例
func NewTaskDao(db *gorm.DB) TaskDao {
	return &taskDao{
		db: db,
	}
}

// Create 创建任务
func (d *taskDao) Create(task *model.Task) error {
	return d.db.Create(task).Error
}

// GetByID 根据ID获取任务
func (d *taskDao) GetByID(id uint) (*model.Task, error) {
	var task model.Task
	err := d.db.Where("id = ?", id).First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// Update 更新任务信息
func (d *taskDao) Update(task *model.Task) error {
	return d.db.Save(task).Error
}

// ListByUserID 根据用户ID查询任务列表
func (d *taskDao) ListByUserID(userID uint, page, pageSize int) ([]*model.Task, int64, error) {
	var tasks []*model.Task
	var total int64

	// 统计总数
	err := d.db.Model(&model.Task{}).Where("user_id = ?", userID).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = d.db.Where("user_id = ?", userID).
		Offset(offset).Limit(pageSize).
		Order("created_at DESC").
		Find(&tasks).Error

	return tasks, total, err
}

// ListByCreditCode 根据信用代码查询任务列表
func (d *taskDao) ListByCreditCode(creditCode string, page, pageSize int) ([]*model.Task, int64, error) {
	var tasks []*model.Task
	var total int64

	// 统计总数
	err := d.db.Model(&model.Task{}).Where("credit_code = ?", creditCode).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = d.db.Where("credit_code = ?", creditCode).
		Offset(offset).Limit(pageSize).
		Order("created_at DESC").
		Find(&tasks).Error

	return tasks, total, err
}

// ListByTimeRange 根据时间区间查询任务列表
func (d *taskDao) ListByTimeRange(startTime, endTime time.Time, page, pageSize int) ([]*model.Task, int64, error) {
	var tasks []*model.Task
	var total int64

	// 统计总数
	err := d.db.Model(&model.Task{}).
		Where("created_at >= ? AND created_at <= ?", startTime, endTime).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = d.db.Where("created_at >= ? AND created_at <= ?", startTime, endTime).
		Offset(offset).Limit(pageSize).
		Order("created_at DESC").
		Find(&tasks).Error

	return tasks, total, err
}

// List 分页查询任务列表（支持多条件筛选）
func (d *taskDao) List(page, pageSize int, userID *uint, creditCode string, status *model.TaskStatus, startTime, endTime *time.Time) ([]*model.Task, int64, error) {
	var tasks []*model.Task
	var total int64

	// 构建查询条件
	query := d.db.Model(&model.Task{})

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}
	if creditCode != "" {
		query = query.Where("credit_code = ?", creditCode)
	}
	if status != nil {
		query = query.Where("status = ?", *status)
	}
	if startTime != nil && endTime != nil {
		query = query.Where("created_at >= ? AND created_at <= ?", *startTime, *endTime)
	}

	// 统计总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).
		Order("created_at DESC").
		Find(&tasks).Error

	return tasks, total, err
}
