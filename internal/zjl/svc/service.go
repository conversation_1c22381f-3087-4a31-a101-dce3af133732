package svc

import (
	"gitee.com/rcztcs/zjl/internal/zjl/dao"
	"gitee.com/rcztcs/zjl/internal/zjl/service"
	"gorm.io/gorm"
)

type ServiceContext struct {
	UserService         service.UserService
	RoleService         service.RoleService
	PermissionService   service.PermissionService
	UserRoleService     service.UserRoleService
	OperationLogService service.OperationLogService
	TaskService         service.TaskService
	JWTService          service.JWTService
	FileService         service.FileService
}

func NewServiceContext(db *gorm.DB, jwtKey string) *ServiceContext {
	// 初始化DAO层
	daoCtx := dao.InitDao(db)

	roleService := service.NewRoleService(daoCtx.RoleDao())
	permissionService := service.NewPermissionService(daoCtx.PermissionDao())
	userRoleService := service.NewUserRoleService(daoCtx.UserRoleDao(), daoCtx.UserDao(), daoCtx.RoleDao())
	operationLogService := service.NewOperationLogService(daoCtx.OperationLogDao())
	taskService := service.NewTaskService(daoCtx.TaskDao())
	fileService := service.NewFileService(daoCtx.FileDao())

	return &ServiceContext{
		UserService:         service.NewUserService(daoCtx.UserDao()),
		PermissionService:   permissionService,
		RoleService:         roleService,
		UserRoleService:     userRoleService,
		OperationLogService: operationLogService,
		TaskService:         taskService,
		JWTService:          service.NewJWTService(jwtKey),
		FileService:         fileService,
	}
}
